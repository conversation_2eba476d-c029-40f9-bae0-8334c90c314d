{"name": "zenith", "version": "1.0.0", "description": "Task Management Application", "scripts": {"install:all": "bun install && cd frontend && bun install && cd ../backend && bun install", "build:frontend": "cd frontend && bun run build", "build:backend": "cd backend && bun build ./src/server.ts --outdir ./dist --target node", "build": "IS_PROD=production bun run build:frontend && bun run build:backend", "start": "cd backend && IS_PROD=production  bun run dist/server.js", "dev:frontend": "cd frontend && bun run dev", "dev:backend": "cd backend && bun run dev", "dev": "concurrently \"bun run dev:frontend\" \"bun run dev:backend\"", "start:full": "bun run build && bun run start"}, "devDependencies": {"concurrently": "^8.2.2"}}