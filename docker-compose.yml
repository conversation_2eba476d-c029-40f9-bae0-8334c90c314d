version: '3.8'

services:
  mongodb:
    image: mongo:latest
    container_name: zenith-mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USER}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    networks:
      - zenith-network

  frontend:
    image: ${DOCKER_USERNAME}/zenith-frontend:latest
    container_name: zenith-frontend
    volumes:
      - frontend_build:/app/frontend/dist
    networks:
      - zenith-network

  backend:
    image: ${DOCKER_USERNAME}/zenith-backend:latest
    container_name: zenith-backend
    ports:
      - "3000:3000"
    environment:
      - IS_PROD=production
      - DATABASE_URL=mongodb://${MONGO_USER}:${MONGO_PASSWORD}@mongodb:27017/zenith?authSource=admin
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - frontend_build:/app/frontend/dist:ro
    depends_on:
      - mongodb
      - frontend
    networks:
      - zenith-network

networks:
  zenith-network:
    driver: bridge

volumes:
  mongodb_data:
  frontend_build: 
