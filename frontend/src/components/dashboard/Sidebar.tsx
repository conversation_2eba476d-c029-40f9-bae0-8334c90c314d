//frontend/src/components/dashboard/Sidebar.tsx
import { NavLink } from "react-router-dom";
import { LayoutDashboard, Users, CheckSquare } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

const navigation = [
  { name: "Summary", href: "/dashboard/home", icon: LayoutDashboard },
  { name: "Organizations", href: "/dashboard/organizations", icon: Users },
  { name: "Tasks", href: "/dashboard/tasks", icon: CheckSquare },
];

export default function Sidebar() {
  return (
    <motion.aside
      className="w-64 bg-[#171717] text-gray-100 p-4 shadow-lg"
      initial={{ x: -250 }}
      animate={{ x: 0 }}
      transition={{ duration: 0.5 }}
    >
      <nav className="mt-8 flex-1 px-2 space-y-1">
        {navigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              cn(
                isActive
                  ? "bg-[#2C2C2C] text-white"
                  : "text-gray-300 hover:bg-[#2C2C2C] hover:text-white",
                "group flex items-center px-2 py-2 text-sm font-medium rounded-md"
              )
            }
          >
            <item.icon
              className="mr-3 flex-shrink-0 h-6 w-6 text-blue-400"
              aria-hidden="true"
            />
            {item.name}
          </NavLink>
        ))}
      </nav>
    </motion.aside>
  );
}
