//frontend/src/components/dashboard/Dashboard.tsx
import { useEffect } from "react";
import { motion } from "framer-motion";

import Header from "./Header";
import Sidebar from "./Sidebar";

import { useAtom } from "jotai";
import { userProfileAtom } from "@/atoms/authAtom";
import { authAtom } from "@/atoms/pageAtom";

export default function Dashboard() {
  const [, setUserProfile] = useAtom(userProfileAtom);
  const [auth] = useAtom(authAtom);

  useEffect(() => {
    const initializeUserProfile = () => {
      if (auth.user) {
        setUserProfile((prev) => ({
          ...prev,
          id: auth.user.id,
          fullName: auth.user.fullName,
          email: auth.user.email,
          createdAt: auth.user.createdAt,
          stats: prev?.stats || {
            totalOrganizations: 0,
            totalTasks: 0,
            pendingTasks: 0,
            completedTasks: 0,
            inProgressTasks: 0,
          },
        }));
      }
    };

    initializeUserProfile();
  }, [auth.user, setUserProfile]);

  return (
    <div className="flex h-screen bg-[#09090b] text-gray-100">
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />

        <motion.main
          className="flex-1 overflow-x-hidden overflow-y-auto bg-[#212121] p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        ></motion.main>
      </div>
    </div>
  );
}
