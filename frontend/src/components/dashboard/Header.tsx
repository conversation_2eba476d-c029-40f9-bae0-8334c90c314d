//frontend/src/components/dashboard/Header.tsx
import { LogOut, User } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "../ui/button";
import { useAuthContext } from "@/context/AuthContext";
import { toast } from "../hooks/use-toast";
import { useState } from "react";
import Modal from "../ui/Modal";
import Profile from "./Profile";

export default function Header() {
  const { logout } = useAuthContext();
  const [showProfileModal, setShowProfileModal] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Success",
        description: "Logged out successfully",
        type: "success",
      });
    } catch (error) {
      console.error("Logout failed:", error);
      toast({
        title: "Error",
        description: "Failed to logout",
        type: "error",
      });
    }
  };

  return (
    <>
      <motion.header
        className="bg-[#2e2e2e] shadow-md p-4 flex justify-between items-center"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center">
          <h2 className="text-xl font-semibold text-white">Dashboard</h2>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-300 hover:bg-[#212121] hover:text-gray-100 focus:bg-[#4d4d4d] focus:text-gray-100 transition duration-200 cursor-pointer"
            onClick={() => setShowProfileModal(true)}
          >
            <User className="h-5 w-5" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="text-red-500 hover:bg-red-500 hover:text-gray-100 cursor-pointer transition duration-200"
            onClick={handleLogout}
          >
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </motion.header>

      {/* Profile Modal */}
      <Modal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
        title="My Profile"
      >
        <Profile />
      </Modal>
    </>
  );
}
