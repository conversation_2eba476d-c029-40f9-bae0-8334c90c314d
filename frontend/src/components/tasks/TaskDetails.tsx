import React from "react";
import { toast } from "@/components/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { useTask } from "@/hooks/useTask";
import { useTasks } from "@/hooks/useTasks";
import { format } from "date-fns";
import { useAuth } from "@/hooks/useAuth";

interface TaskDetailsProps {
  taskId: string;
  onClose: () => void;
  onUpdate: () => void;
  onDelete: () => void;
  isOwner: boolean;
}

export default function TaskDetails({
  taskId,
  onClose,
  onUpdate,
  onDelete,
  isOwner,
}: TaskDetailsProps) {
  const { user } = useAuth();
  const { data: task, isLoading } = useTask(taskId);
  const { updateTaskStatus, deleteTask, isUpdatingStatus, isDeleting } =
    useTasks();

  // Check if current user can edit status (owner or assignee)
  const canEditStatus = isOwner || task?.assignee?.id === user?.id;

  const handleStatusChange = async (
    newStatus: "pending" | "in_progress" | "completed"
  ) => {
    if (!task) return;

    try {
      await updateTaskStatus({ taskId: task.id, status: newStatus });
      toast({
        title: "Success",
        description: "Task status updated successfully",
        type: "default",
      });
      onUpdate();
    } catch (error) {
      console.error("Failed to update task status:", error);
      toast({
        title: "Error",
        description: "Failed to update task status",
        type: "error",
      });
    }
  };

  const handleDelete = async () => {
    if (!task) return;

    try {
      await deleteTask(task.id);
      toast({
        title: "Success",
        description: "Task deleted successfully",
        type: "default",
      });
      onDelete();
    } catch (error) {
      console.error("Failed to delete task:", error);
      toast({
        title: "Error",
        description: "Failed to delete task",
        type: "error",
      });
    }
  };

  if (isLoading || !task) {
    return (
      <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
        <div className="bg-[#171717] border border-[#333333] rounded-lg p-6 w-full max-w-2xl">
          <div className="flex items-center justify-center p-10">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-[#171717] border border-[#333333] rounded-lg p-6 w-full max-w-2xl">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-xl font-bold text-white">{task.title}</h2>
          <Button
            variant="ghost"
            onClick={onClose}
            className="text-gray-400 hover:text-white hover:bg-gray-700 p-2 h-8 w-8 flex items-center justify-center"
          >
            <span className="text-lg font-bold">×</span>
          </Button>
        </div>

        <div className="space-y-4">
          {task.description && (
            <div>
              <h3 className="text-sm font-medium text-gray-400">Description</h3>
              <p className="mt-1 text-white">{task.description}</p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-400">Status</h3>
              <div className="mt-1">
                <select
                  value={task.status}
                  onChange={(e) =>
                    handleStatusChange(
                      e.target.value as "pending" | "in_progress" | "completed"
                    )
                  }
                  disabled={!canEditStatus || isUpdatingStatus}
                  className="w-full p-2 bg-[#212121] border border-[#404040] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-400">Due Date</h3>
              <p className="mt-1 text-white">
                {format(new Date(task.dueAt), "PPP")}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-400">Created By</h3>
              <p className="mt-1 text-white">{task.creator?.fullName}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-400">Assignee</h3>
              <p className="mt-1 text-white">
                {task.assignee?.fullName || "Unassigned"}
              </p>
            </div>

            {task.organization && (
              <div>
                <h3 className="text-sm font-medium text-gray-400">
                  Organization
                </h3>
                <p className="mt-1 text-white">{task.organization.name}</p>
              </div>
            )}
          </div>

          {isOwner && (
            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete Task"}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
