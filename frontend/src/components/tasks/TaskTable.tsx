import { Task } from "@/services/task.service";
import { Button } from "../ui/button";

interface TaskTableProps {
  tasks: Task[];
  orgId?: string;
  onViewTask: (taskId: string) => void;
  onEditTask: (task: Task) => void;
  canEditTask: (task: Task) => boolean;
  isUpdating: boolean;
  isUpdatingStatus: boolean;
  isDeleting: boolean;
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case "pending":
      return "bg-yellow-900 text-yellow-300";
    case "in-progress":
      return "bg-blue-900 text-blue-300";
    case "completed":
      return "bg-green-900 text-green-300";
    default:
      return "bg-gray-700 text-gray-300";
  }
};

export default function TaskTable({
  tasks,
  orgId,
  onViewTask,
  onEditTask,
  canEditTask,
  isUpdating,
  isUpdatingStatus,
  isDeleting,
}: TaskTableProps) {
  if (tasks.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-400">No tasks found.</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-700">
        <thead className="bg-[#171717]">
          <tr>
            <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
              Title
            </th>
            <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
              Status
            </th>
            <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
              Due Date
            </th>
            {!orgId && (
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
                Organization
              </th>
            )}
            <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
              Assignee
            </th>
            <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-700">
          {tasks.map((task) => (
            <tr
              key={task.id}
              className="hover:bg-[#1f1f1f] transition-colors"
            >
              <td className="px-4 py-3 text-sm text-white">
                {task.title}
              </td>
              <td className="px-4 py-3">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(
                    task.status
                  )}`}
                >
                  {task.status}
                </span>
              </td>
              <td className="px-4 py-3 text-sm text-white">
                {new Date(task.dueAt).toLocaleDateString()}
              </td>
              {!orgId && (
                <td className="px-4 py-3 text-sm text-white">
                  {task.organization?.name}
                </td>
              )}
              <td className="px-4 py-3 text-sm text-white">
                {task.assignee?.fullName || "Unassigned"}
              </td>
              <td className="px-4 py-3 text-sm space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewTask(task.id)}
                  disabled={isUpdating || isUpdatingStatus || isDeleting}
                >
                  View
                </Button>
                {canEditTask(task) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditTask(task)}
                    disabled={isUpdating || isUpdatingStatus || isDeleting}
                  >
                    Edit
                  </Button>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
