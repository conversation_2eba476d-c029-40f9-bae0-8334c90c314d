//frontend/src/main.tsx
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'jotai';
import App from './App';
import { QueryProvider } from './providers/QueryProvider';
import './index.css';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider>
      <QueryProvider>
        <App />
      </QueryProvider>
    </Provider>
  </StrictMode>
);
