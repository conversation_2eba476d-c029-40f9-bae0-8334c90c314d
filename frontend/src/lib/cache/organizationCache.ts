export const organizationKeys = {
  all: ['organizations'] as const,
  lists: () => [...organizationKeys.all, 'list'] as const,
  list: (filters?: { status?: string }) => [...organizationKeys.lists(), { filters }] as const,
  details: () => [...organizationKeys.all, 'detail'] as const,
  detail: (id: string) => [...organizationKeys.details(), id] as const,
  stats: (id: string) => [...organizationKeys.detail(id), 'stats'] as const,
  members: (id: string) => [...organizationKeys.detail(id), 'members'] as const,
  tasks: (id: string) => [...organizationKeys.detail(id), 'tasks'] as const,
  inviteCode: (id: string) => [...organizationKeys.detail(id), 'inviteCode'] as const,
}; 