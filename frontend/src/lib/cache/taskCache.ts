import { QueryClient } from '@tanstack/react-query';
import { prefetchQuery, prefetchInfiniteQuery, PaginatedResponse } from '../prefetch';

// Query keys for tasks
export const taskKeys = {
  all: ['tasks'] as const,
  lists: () => [...taskKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...taskKeys.lists(), { ...filters }] as const,
  details: () => [...taskKeys.all, 'detail'] as const,
  detail: (id: string) => [...taskKeys.details(), id] as const,
};

// Prefetch task list with pagination
export const prefetchTaskList = async <T>(
  queryClient: QueryClient,
  fetchFn: (page: number) => Promise<PaginatedResponse<T>>,
  filters: Record<string, any> = {}
) => {
  await prefetchInfiniteQuery<T>(
    queryClient,
    taskKeys.list(filters),
    fetchFn,
    { staleTime: 30 * 1000 } // 30 seconds
  );
};

// Prefetch single task details
export const prefetchTaskDetails = async <T>(
  queryClient: QueryClient,
  taskId: string,
  fetchFn: () => Promise<T>
) => {
  await prefetchQuery<T>(
    queryClient,
    taskKeys.detail(taskId),
    fetchFn,
    { staleTime: 1 * 60 * 1000 } // 1 minute
  );
};

// Optimistic update helpers
export const optimisticUpdateTask = <T>(
  queryClient: QueryClient,
  taskId: string,
  updatedData: T
) => {
  queryClient.setQueryData(
    taskKeys.detail(taskId),
    updatedData
  );
};

// Cache invalidation after task mutations
export const invalidateTaskLists = async (
  queryClient: QueryClient,
  filters?: Record<string, any>
) => {
  if (filters) {
    await queryClient.invalidateQueries({
      queryKey: taskKeys.list(filters),
    });
  } else {
    await queryClient.invalidateQueries({
      queryKey: taskKeys.lists(),
    });
  }
}; 