import { QueryClient } from '@tanstack/react-query';
import { prefetchQuery } from '../prefetch';

// Query keys for dashboard data
export const dashboardKeys = {
  all: ['dashboard'] as const,
  overview: () => [...dashboardKeys.all, 'overview'] as const,
  tasks: () => [...dashboardKeys.all, 'tasks'] as const,
  taskById: (id: string) => [...dashboardKeys.tasks(), id] as const,
};

// Prefetch dashboard overview data
export const prefetchDashboardOverview = async <T>(
  queryClient: QueryClient,
  fetchFn: () => Promise<T>
) => {
  await prefetchQuery<T>(
    queryClient,
    dashboardKeys.overview(),
    fetchFn,
    { staleTime: 2 * 60 * 1000 } // 2 minutes
  );
};

// Prefetch tasks list
export const prefetchTasks = async <T>(
  queryClient: QueryClient,
  fetchFn: () => Promise<T>
) => {
  await prefetchQuery<T>(
    queryClient,
    dashboardKeys.tasks(),
    fetchFn,
    { staleTime: 1 * 60 * 1000 } // 1 minute
  );
};

// Invalidate task cache after mutations
export const invalidateTask = async (
  queryClient: QueryClient,
  taskId?: string
) => {
  if (taskId) {
    await queryClient.invalidateQueries({
      queryKey: dashboardKeys.taskById(taskId),
    });
  }
  await queryClient.invalidateQueries({
    queryKey: dashboardKeys.tasks(),
  });
}; 