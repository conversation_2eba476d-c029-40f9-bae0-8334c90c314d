import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Keep cached data for 5 minutes
      staleTime: 5 * 60 * 1000,
      // Retry failed requests 3 times
      retry: 3,
      // Enable automatic background refetching
      refetchOnWindowFocus: true,
      // Cache data even when window/tab is closed
      gcTime: 10 * 60 * 1000,
    },
    mutations: {
      // Retry failed mutations 2 times
      retry: 2,
    },
  },
}); 