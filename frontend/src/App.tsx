//frontend/src/App.tsx
import { Toaster } from "sonner";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
  useLocation,
} from "react-router-dom";
import { useEffect, useState, Suspense, lazy } from "react";
import { useAtom } from "jotai";
import { authAtom } from "./atoms/pageAtom";
import { useAuth } from "./hooks/useAuth";
import Loader from "./components/loaders/loader";
import { AuthProvider } from "./context/AuthContext";
import ErrorBoundary from "./components/common/ErrorBoundary";

// Lazy load components for better code splitting
const Login = lazy(() => import("./pages/Login"));
const Signup = lazy(() => import("./pages/Signup"));
const DashboardLayout = lazy(() => import("./pages/dashboard/Layout"));
const Summary = lazy(() => import("./components/dashboard/Summary"));
const Tasks = lazy(() => import("./components/dashboard/Tasks"));
const OrganizationRoutes = lazy(() => import("@/routes/organization.routes"));

// Auth Guard for public routes (login/signup)
function PublicRoute({ children }: { children: React.ReactNode }) {
  const [auth] = useAtom(authAtom);
  const location = useLocation();

  if (auth.isAuthenticated) {
    return <Navigate to="/dashboard" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}

// Auth Guard for protected routes
function PrivateRoute({ children }: { children: React.ReactNode }) {
  const [auth] = useAtom(authAtom);
  const location = useLocation();

  if (!auth.isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}

const App = () => {
  const [isHydrated, setIsHydrated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { checkAuth } = useAuth();

  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsLoading(true);
        await checkAuth();
      } finally {
        setIsHydrated(true);
        setIsLoading(false);
      }
    };
    initAuth();
  }, [checkAuth]);

  if (!isHydrated || isLoading) {
    return <Loader />;
  }

  return (
    <ErrorBoundary>
      <Router>
        <AuthProvider>
          <div className="bg-[#09090B] min-h-screen">
            <Toaster position="top-center" />
            <Routes>
              {/* Public Routes */}
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <Suspense fallback={<Loader />}>
                      <Login />
                    </Suspense>
                  </PublicRoute>
                }
              />
              <Route
                path="/signup"
                element={
                  <PublicRoute>
                    <Suspense fallback={<Loader />}>
                      <Signup />
                    </Suspense>
                  </PublicRoute>
                }
              />

              {/* Protected Dashboard Routes */}
              <Route
                path="/dashboard"
                element={
                  <PrivateRoute>
                    <Suspense fallback={<Loader />}>
                      <DashboardLayout />
                    </Suspense>
                  </PrivateRoute>
                }
              >
                {/* Add redirect for /dashboard */}
                <Route
                  index
                  element={<Navigate to="/dashboard/home" replace />}
                />
                {/* Remove path="home" from index route */}
                <Route
                  path="home"
                  element={
                    <Suspense fallback={<Loader />}>
                      <Summary />
                    </Suspense>
                  }
                />
                {/* Organization Routes */}
                <Route
                  path="organizations/*"
                  element={
                    <Suspense fallback={<Loader />}>
                      <OrganizationRoutes />
                    </Suspense>
                  }
                />
                <Route
                  path="tasks"
                  element={
                    <Suspense fallback={<Loader />}>
                      <Tasks />
                    </Suspense>
                  }
                />
              </Route>

              {/* Default redirect */}
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </div>
        </AuthProvider>
      </Router>
    </ErrorBoundary>
  );
};

export default App;
