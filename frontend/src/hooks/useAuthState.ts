import { useAtom } from 'jotai';
import { authAtom } from '@/atoms/pageAtom';
import { useAuth } from './useAuth';

/**
 * Consolidated hook for authentication state management
 * Combines authAtom and useAuth functionality
 */
export function useAuthState() {
  const [auth, setAuth] = useAtom(authAtom);
  const { checkAuth, login, signup, logout } = useAuth();

  const updateAuthState = (newState: Partial<typeof auth>) => {
    setAuth(prev => ({ ...prev, ...newState }));
  };

  const clearAuthState = () => {
    setAuth({
      isAuthenticated: false,
      user: null,
      token: null
    });
  };

  return {
    // State
    auth,
    isAuthenticated: auth.isAuthenticated,
    user: auth.user,
    token: auth.token,
    
    // Actions
    updateAuthState,
    clearAuthState,
    checkAuth,
    login,
    signup,
    logout,
  };
}
