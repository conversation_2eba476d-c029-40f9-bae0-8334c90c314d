import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getUserOrganizations,
  getOrganizationById,
  createOrganization,
  updateOrganization,
  deleteOrganization,
  generateJoinCode,
  joinOrganization,
  type Organization,
  type CreateOrgData,
} from "@/services/organization.service";
import { organizationKeys } from "@/lib/cache/organizationCache";

interface UseOrganizationsOptions {
  filters?: {
    status?: string;
  };
}

export function useOrganizations(options: UseOrganizationsOptions = {}) {
  const queryClient = useQueryClient();

  // Fetch organizations query
  const {
    data: organizations,
    isLoading,
    error,
  } = useQuery({
    queryKey: organizationKeys.list(options.filters),
    queryFn: () => getUserOrganizations(),
    staleTime: 30 * 1000, // Consider data fresh for 30 seconds
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  // Create organization mutation
  const createOrganizationMutation = useMutation({
    mutationFn: (data: CreateOrgData) => createOrganization(data),
    onSuccess: (newOrg) => {
      // Update organizations list in cache
      queryClient.setQueryData<Organization[]>(
        organizationKeys.list(),
        (old = []) => [...old, newOrg]
      );
    },
  });

  // Update organization mutation
  const updateOrganizationMutation = useMutation({
    mutationFn: ({
      orgId,
      data,
    }: {
      orgId: string;
      data: Partial<CreateOrgData>;
    }) => updateOrganization(orgId, data),
    onSuccess: (updatedOrg) => {
      // Update organization in cache
      queryClient.setQueryData(
        organizationKeys.detail(updatedOrg.id),
        updatedOrg
      );
      // Update organizations list
      queryClient.setQueryData<Organization[]>(
        organizationKeys.list(),
        (old = []) =>
          old?.map((org) =>
            org.id === updatedOrg.id ? { ...org, ...updatedOrg } : org
          )
      );
    },
  });

  // Delete organization mutation
  const deleteOrganizationMutation = useMutation({
    mutationFn: deleteOrganization,
    onSuccess: (_, orgId) => {
      // Remove organization from cache
      queryClient.removeQueries({
        queryKey: organizationKeys.detail(orgId),
      });
      // Update organizations list
      queryClient.setQueryData<Organization[]>(
        organizationKeys.list(),
        (old = []) => old?.filter((org) => org.id !== orgId)
      );
    },
  });

  // Join organization mutation
  const joinOrganizationMutation = useMutation({
    mutationFn: joinOrganization,
    onSuccess: (newOrg) => {
      // Add organization to list
      queryClient.setQueryData<Organization[]>(
        organizationKeys.list(),
        (old = []) => [...(old || []), newOrg]
      );
    },
  });

  // Generate invite code mutation
  const generateInviteCodeMutation = useMutation({
    mutationFn: generateJoinCode,
    onSuccess: (inviteCode, orgId) => {
      // Update organization in cache with new invite code
      queryClient.setQueryData<Organization>(
        organizationKeys.detail(orgId),
        (old) => (old ? { ...old, inviteCode } : undefined)
      );
    },
  });

  return {
    organizations,
    isLoading,
    error,
    createOrganization: createOrganizationMutation.mutate,
    updateOrganization: updateOrganizationMutation.mutate,
    deleteOrganization: deleteOrganizationMutation.mutate,
    joinOrganization: joinOrganizationMutation.mutate,
    generateInviteCode: generateInviteCodeMutation.mutate,
    isCreating: createOrganizationMutation.isPending,
    isUpdating: updateOrganizationMutation.isPending,
    isDeleting: deleteOrganizationMutation.isPending,
    isJoining: joinOrganizationMutation.isPending,
    isGeneratingCode: generateInviteCodeMutation.isPending,
  };
}

export function useOrganization(orgId?: string) {
  const queryClient = useQueryClient();

  // Fetch single organization
  const {
    data: organization,
    isLoading,
    error,
  } = useQuery({
    queryKey: orgId ? organizationKeys.detail(orgId) : undefined,
    queryFn: () => (orgId ? getOrganizationById(orgId) : null),
    enabled: !!orgId,
    staleTime: 30 * 1000, // Consider data fresh for 30 seconds
    refetchOnMount: true,
    refetchOnWindowFocus: false, // Disable refetch on window focus to prevent issues after deletion
    retry: (failureCount, error: any) => {
      // Don't retry if it's a 403/404 error (organization deleted or access denied)
      if (error?.response?.status === 403 || error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // Prefetch organization data
  const prefetchOrganization = async (id: string) => {
    await queryClient.prefetchQuery({
      queryKey: organizationKeys.detail(id),
      queryFn: () => getOrganizationById(id),
      staleTime: 30 * 1000,
    });
  };

  return {
    organization,
    isLoading,
    error,
    prefetchOrganization,
  };
}
