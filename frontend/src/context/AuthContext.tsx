import { createContext, useContext, useEffect, ReactNode } from "react";
import { useNavigate } from "react-router-dom";
import { useAtom } from "jotai";
import { authAtom } from "../atoms/pageAtom";
import { useAuth } from "../hooks/useAuth";

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const navigate = useNavigate();
  const [auth, setAuth] = useAtom(authAtom);
  const { checkAuth, logout: logoutFn } = useAuth();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const logout = async () => {
    await logoutFn();
    setAuth({ isAuthenticated: false, user: null });
    navigate("/login", { replace: true });
  };

  const value = {
    isAuthenticated: auth.isAuthenticated,
    isLoading: false,
    user: auth.user,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
} 