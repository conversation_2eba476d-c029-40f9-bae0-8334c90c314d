// vite.config.js
import path from "path";
import { defineConfig } from "vite";
import tailwindcss from "@tailwindcss/vite";

// For React projects
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [
    react(), // Include this line for React projects
    tailwindcss(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          "react-vendor": ["react", "react-dom"],
          router: ["react-router-dom"],
          "ui-vendor": [
            "@radix-ui/react-dialog",
            "@radix-ui/react-label",
            "@radix-ui/react-slot",
          ],
          query: ["@tanstack/react-query"],
          form: ["react-hook-form", "@hookform/resolvers"],
          animation: ["framer-motion"],
          icons: ["lucide-react"],
          utils: ["clsx", "class-variance-authority", "tailwind-merge"],
          validation: ["zod"],
          http: ["axios"],
          state: ["jotai"],
          toast: ["sonner"],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
    sourcemap: false,
    minify: "esbuild",
  },
});
