# Zenith - Task Management Application

Zenith is a modern task management application built with React, TypeScript, and Express, using Bun as the JavaScript runtime. It features organization management, task tracking, and real-time collaboration capabilities.

## 🚀 Features

- **User Authentication**: Secure login and registration system
- **Organization Management**: Create and join organizations with invite codes
- **Task Management**: Create, assign, and track tasks within organizations
- **Real-time Updates**: Live task and organization updates
- **Responsive Design**: Modern UI built with Tailwind CSS and Radix UI
- **Docker Support**: Containerized deployment with Docker and Docker Compose

## 🛠 Tech Stack

### Frontend
- React 19
- TypeScript
- Vite
- Tailwind CSS
- Radix UI Components
- Framer Motion
- Jotai (State Management)

### Backend
- Express.js
- Prisma (MongoDB)
- JWT Authentication
- Bun Runtime

### Infrastructure
- Docker & Docker Compose
- MongoDB
- GitHub Actions (CI/CD)

## 📋 Prerequisites

- [Bun](https://bun.sh) (v1.2.2 or higher)
- [Docker](https://www.docker.com/) (for containerized deployment)
- [MongoDB](https://www.mongodb.com/) (local or containerized)

## 🚦 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd zenith
   ```

2. **Environment Setup**
   ```bash
   # Copy example environment file
   cp .env.example .env
   # Update environment variables as needed
   ```

3. **Install Dependencies**
   ```bash
   # Install all dependencies (frontend and backend)
   bun run install:all
   ```

4. **Development Mode**
   ```bash
   # Run frontend and backend in development mode
   bun run dev
   
   # Or run them separately
   bun run dev:frontend  # Frontend on http://localhost:5173
   bun run dev:backend   # Backend on http://localhost:3000
   ```

5. **Production Build**
   ```bash
   # Build both frontend and backend
   bun run build
   ```

## 🐳 Docker Deployment

1. **Build and Run with Docker Compose**
   ```bash
   docker-compose up --build
   ```

2. **Access the Application**
   - Frontend: `http://localhost:3000`
   - Backend API: `http://localhost:3000/api`
   - MongoDB: `mongodb://localhost:27017`

## 📁 Project Structure

```
zenith/
├── frontend/           # React frontend application
├── backend/           # Express backend application
├── docker-compose.yml # Docker composition
└── package.json      # Root package.json for project management
```

## 🔑 Environment Variables

Required environment variables in `.env`:
- `MONGO_USER`: MongoDB username
- `MONGO_PASSWORD`: MongoDB password
- `JWT_SECRET`: Secret key for JWT tokens
- `IS_PROD`: Production environment flag
- `DATABASE_URL`: MongoDB connection string

## 👥 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
