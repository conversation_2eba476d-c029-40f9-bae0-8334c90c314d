# Stage 1: Build the backend
FROM oven/bun:1

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY frontend/dist ./frontend/dist

# Install dependencies
RUN cd backend && bun install

# Copy backend source code
COPY backend/ ./backend/

# Build the backend
RUN cd backend && bun run build

# Set production environment
ENV IS_PROD=production

# Expose the port
EXPOSE 3000

# Start the server
WORKDIR /app/backend
CMD ["bun", "run", "start"] 