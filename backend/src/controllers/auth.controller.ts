// backend/src/controllers/auth.controller.ts
import { prisma } from "../lib/prisma";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import type { Request, Response } from "express";

// Signup Function
export const signup = async (req: Request, res: Response): Promise<void> => {
  try {
    const { fullName, email, password } = req.body;

    // Input validation
    if (!fullName || !email || !password) {
      res.status(400).json({ message: "All fields are required" });
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      res.status(400).json({ message: "Invalid email format" });
      return;
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({ where: { email } });
    if (existingUser) {
      res.status(400).json({ message: "User already exists" });
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const user = await prisma.user.create({
      data: { fullName, email, password: hashedPassword },
    });

    // Generate JWT token
    const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET!, {
      expiresIn: "7d", // Token valid for 7 days
    });

    // Send response
    res
      .status(201)
      .cookie("token", token, {
        httpOnly: true,
        secure: process.env.IS_PROD === "production",
        sameSite: "strict",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      })
      .json({
        message: "User created successfully",
        user: {
          id: user.id,
          fullName: user.fullName,
          email: user.email,
        },
        token,
      });
  } catch (error) {
    console.error("Signup Error:", error);
    res.status(500).json({ message: "Error creating user" });
  }
};

// Login Function
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      res.status(400).json({ message: "Invalid email or password" });
      return;
    }

    // Verify password
    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      res.status(400).json({ message: "Invalid email or password" });
      return;
    }

    // Generate JWT token
    const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET!, {
      expiresIn: "7d", // Token valid for 7 days
    });

    // Send response
    res
      .cookie("token", token, {
        httpOnly: true,
        secure: process.env.IS_PROD === "production",
        sameSite: "strict",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      })
      .json({
        message: "Login successful",
        user: {
          id: user.id,
          fullName: user.fullName,
          email: user.email,
          createdAt: user.createdAt,
        },
        token,
      });
  } catch (error) {
    console.error("Login Error:", error);
    res.status(500).json({ message: "Error logging in" });
  }
};

// Logout Function
export const logout = async (req: Request, res: Response): Promise<void> => {
  try {
    res.clearCookie("token").json({ message: "Logged out successfully" });
  } catch (error) {
    console.error("Logout Error:", error);
    res.status(500).json({ message: "Error logging out" });
  }
};

// Get Current User
export const getMe = async (req: Request, res: Response): Promise<void> => {
  try {
    // User is attached to request in auth middleware
    res.json({
      user: req.user,
    });
  } catch (error) {
    console.error("Get Me Error:", error);
    res.status(500).json({ message: "Error retrieving user" });
  }
};
