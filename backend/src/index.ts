import express from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import path from "node:path";
import { fileURLToPath } from "node:url";

import { errorHandler } from "./middleware/error.middleware";
import authRoutes from "./routes/auth.routes";
import userRoutes from "./routes/user.routes";
import organizationRoutes from "./routes/organization.routes";
import membershipRoutes from "./routes/membership.routes";
import taskRoutes from "./routes/task.routes";

const createApp = () => {
  const app = express();
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);

  app.use(express.json());
  app.use(cookieParser());
  app.use(
    cors({
      // origin:"http://localhost:5173",
      origin:process.env.IS_PROD === "production" ? false : "http://localhost:5173",
      credentials: true,
      methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
      allowedHeaders: "Content-Type,Authorization",
    }),
  );

  // API Routes
  app.use("/api/auth", authRoutes);
  app.use("/api/users", userRoutes);
  app.use("/api/organizations", organizationRoutes);
  app.use("/api/memberships", membershipRoutes);
  app.use("/api/tasks", taskRoutes);

  // Serve static files in production
  if (process.env.IS_PROD === "production") {
    // Calculate the correct path to the frontend dist directory
    const frontendBuildPath = path.resolve(__dirname, "../../frontend/dist");
    console.log("🔍 Serving frontend from:", frontendBuildPath);

    // Serve static files
    app.use(express.static(frontendBuildPath));

    // Handle client-side routing
    app.get("*", (req, res, next) => {
      // Skip API routes - don't return the response directly
      if (req.path.startsWith("/api/")) {
        res.status(404).json({ message: "API endpoint not found" });
      } else {
        res.sendFile(path.join(frontendBuildPath, "index.html"));
      }
      // No return statement at all - this satisfies TypeScript
    });
  }

  // Error handling middleware
  app.use(errorHandler);
  return app;
};

// Explicitly export app creation for bundled use
export { createApp };
