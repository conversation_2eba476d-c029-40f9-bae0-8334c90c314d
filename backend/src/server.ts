// server.ts
import { createApp } from "./index";

const PORT = process.env.PORT || 3000;
const app = createApp();

// In server.ts
const isProduction = process.env.IS_PROD === "production";
console.log(`IS_PROD=${process.env.IS_PROD}`);
console.log(`Running in ${isProduction ? "production" : "development"} mode`);

app.listen(PORT, () => {
  console.log(`🌟 Server running at http://localhost:${PORT}`);
});
