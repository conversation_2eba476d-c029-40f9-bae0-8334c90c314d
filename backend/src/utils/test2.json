{"info": {"_postman_id": "e93bf4d8-7f31-4d0a-8f39-1a5c516afc13", "name": "Backend API", "description": "Complete API collection for testing the backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"Test User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "http://localhost:3000/api/auth/signup", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "signup"]}}, "response": []}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "http://localhost:3000/api/auth/login", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/auth/me", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "me"]}}, "response": []}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/auth/logout", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "logout"]}}, "response": []}]}, {"name": "User", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/users/profile", "host": ["localhost"], "port": "3000", "path": ["api", "users", "profile"]}}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"Updated Name\"\n}"}, "url": {"raw": "http://localhost:3000/api/users/profile", "host": ["localhost"], "port": "3000", "path": ["api", "users", "profile"]}}, "response": []}, {"name": "Get User Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/users/stats", "host": ["localhost"], "port": "3000", "path": ["api", "users", "stats"]}}, "response": []}]}, {"name": "Organization", "item": [{"name": "Create Organization", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Organization\",\n  \"description\": \"This is a test organization\"\n}"}, "url": {"raw": "http://localhost:3000/api/organizations", "host": ["localhost"], "port": "3000", "path": ["api", "organizations"]}}, "response": []}, {"name": "Get All Organizations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/organizations", "host": ["localhost"], "port": "3000", "path": ["api", "organizations"]}}, "response": []}, {"name": "Get Organization by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/organizations/{{orgId}}", "host": ["localhost"], "port": "3000", "path": ["api", "organizations", "{{orgId}}"]}}, "response": []}, {"name": "Update Organization", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Organization\",\n  \"description\": \"This organization has been updated\"\n}"}, "url": {"raw": "http://localhost:3000/api/organizations/{{orgId}}", "host": ["localhost"], "port": "3000", "path": ["api", "organizations", "{{orgId}}"]}}, "response": []}, {"name": "Join Organization", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"inviteCode\": \"{{inviteCode}}\"\n}"}, "url": {"raw": "http://localhost:3000/api/organizations/join", "host": ["localhost"], "port": "3000", "path": ["api", "organizations", "join"]}}, "response": []}, {"name": "Delete Organization", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/organizations/{{orgId}}", "host": ["localhost"], "port": "3000", "path": ["api", "organizations", "{{orgId}}"]}}, "response": []}]}, {"name": "Membership", "item": [{"name": "Get Organization Members", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/memberships/organization/{{orgId}}", "host": ["localhost"], "port": "3000", "path": ["api", "memberships", "organization", "{{orgId}}"]}}, "response": []}, {"name": "Remove Member", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/memberships/organization/{{orgId}}/member/{{memberId}}", "host": ["localhost"], "port": "3000", "path": ["api", "memberships", "organization", "{{orgId}}", "member", "{{memberId}}"]}}, "response": []}]}, {"name": "Task", "item": [{"name": "Create Task", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Task\",\n  \"description\": \"This is a test task\",\n  \"orgId\": \"{{orgId}}\",\n  \"dueAt\": \"2025-03-30T23:59:59Z\"\n}"}, "url": {"raw": "http://localhost:3000/api/tasks", "host": ["localhost"], "port": "3000", "path": ["api", "tasks"]}}, "response": []}, {"name": "Get Tasks by Organization", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/tasks/org/{{orgId}}", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "org", "{{orgId}}"]}}, "response": []}, {"name": "Get Task by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/tasks/{{taskId}}", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "{{taskId}}"]}}, "response": []}, {"name": "Update Task Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in-progress\"\n}"}, "url": {"raw": "http://localhost:3000/api/tasks/{{taskId}}/status", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "{{taskId}}", "status"]}}, "response": []}, {"name": "Assign Task", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assignedTo\": \"{{userId}}\"\n}"}, "url": {"raw": "http://localhost:3000/api/tasks/{{taskId}}/assign", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "{{taskId}}", "assign"]}}, "response": []}, {"name": "Update Task", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Task Title\",\n  \"description\": \"This task has been updated\",\n  \"dueAt\": \"2025-04-01T23:59:59Z\"\n}"}, "url": {"raw": "http://localhost:3000/api/tasks/{{taskId}}", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "{{taskId}}"]}}, "response": []}, {"name": "Delete Task", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "http://localhost:3000/api/tasks/{{taskId}}", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "{{taskId}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "token", "value": ""}, {"key": "userId", "value": ""}, {"key": "orgId", "value": ""}, {"key": "inviteCode", "value": ""}, {"key": "taskId", "value": ""}, {"key": "memberId", "value": ""}]}