{"info": {"_postman_id": "b5e21a8a-d934-43e8-90d3-e4dc0e0f1b7c", "name": "Backend API", "description": "Complete API collection for testing the backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"Test User\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/signup", "host": ["{{baseUrl}}"], "path": ["api", "auth", "signup"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "", "pm.test(\"Response has user and token\", function () {", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData).to.have.property('token');", "    pm.expect(jsonData.user).to.have.property('id');", "    pm.environment.set(\"userId\", jsonData.user.id);", "    pm.environment.set(\"token\", jsonData.token);", "});"], "type": "text/javascript"}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "var jsonData = pm.response.json();", "", "pm.test(\"Response has user and token\", function () {", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData).to.have.property('token');", "    pm.environment.set(\"userId\", jsonData.user.id);", "    pm.environment.set(\"token\", jsonData.token);", "});"], "type": "text/javascript"}}]}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has user data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData.user).to.have.property('id');", "    pm.expect(jsonData.user).to.have.property('email');", "    pm.expect(jsonData.user).to.have.property('fullName');", "});"], "type": "text/javascript"}}]}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Successfully logged out\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message', 'Logged out successfully');", "});"], "type": "text/javascript"}}]}]}, {"name": "User", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/users/profile", "host": ["{{baseUrl}}"], "path": ["api", "users", "profile"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has user data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData.user).to.have.property('id');", "    pm.expect(jsonData.user).to.have.property('email');", "    pm.expect(jsonData.user).to.have.property('fullName');", "});"], "type": "text/javascript"}}]}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"Updated Name\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/profile", "host": ["{{baseUrl}}"], "path": ["api", "users", "profile"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Profile was updated\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('user');", "    pm.expect(jsonData).to.have.property('message', 'Profile updated successfully');", "    pm.expect(jsonData.user.fullName).to.equal('Updated Name');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/users/stats", "host": ["{{baseUrl}}"], "path": ["api", "users", "stats"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has stats data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('stats');", "    pm.expect(jsonData.stats).to.have.property('totalOrganizations');", "    pm.expect(jsonData.stats).to.have.property('totalTasks');", "});"], "type": "text/javascript"}}]}]}, {"name": "Organization", "item": [{"name": "Create Organization", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Organization\",\n  \"description\": \"This is a test organization\"\n}"}, "url": {"raw": "{{baseUrl}}/api/organizations", "host": ["{{baseUrl}}"], "path": ["api", "organizations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "", "pm.test(\"Organization created successfully\", function () {", "    pm.expect(jsonData).to.have.property('message', 'Organization created successfully');", "    pm.expect(jsonData).to.have.property('organization');", "    pm.expect(jsonData.organization).to.have.property('id');", "    pm.expect(jsonData.organization).to.have.property('joinCode');", "    pm.environment.set(\"orgId\", jsonData.organization.id);", "    pm.environment.set(\"joinCode\", jsonData.organization.joinCode);", "});"], "type": "text/javascript"}}]}, {"name": "Get All Organizations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/organizations", "host": ["{{baseUrl}}"], "path": ["api", "organizations"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has organizations array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('organizations');", "    pm.expect(jsonData.organizations).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Organization by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/organizations/{{orgId}}", "host": ["{{baseUrl}}"], "path": ["api", "organizations", "{{orgId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has organization data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('organization');", "    pm.expect(jsonData.organization).to.have.property('id', pm.environment.get(\"orgId\"));", "    pm.expect(jsonData.organization).to.have.property('name');", "    pm.expect(jsonData.organization).to.have.property('description');", "});"], "type": "text/javascript"}}]}, {"name": "Update Organization", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Organization\",\n  \"description\": \"This organization has been updated\"\n}"}, "url": {"raw": "{{baseUrl}}/api/organizations/{{orgId}}", "host": ["{{baseUrl}}"], "path": ["api", "organizations", "{{orgId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Organization updated successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message', 'Organization updated successfully');", "    pm.expect(jsonData).to.have.property('organization');", "    pm.expect(jsonData.organization.name).to.equal('Updated Organization');", "});"], "type": "text/javascript"}}]}, {"name": "Join Organization", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"joinCode\": \"{{joinCode}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/organizations/join", "host": ["{{baseUrl}}"], "path": ["api", "organizations", "join"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Successfully joined organization\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message', 'Successfully joined organization');", "    pm.expect(jsonData).to.have.property('organization');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Organization", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/organizations/{{orgId}}", "host": ["{{baseUrl}}"], "path": ["api", "organizations", "{{orgId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Organization deleted successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message', 'Organization deleted successfully');", "});"], "type": "text/javascript"}}]}]}, {"name": "Membership", "item": [{"name": "Get Organization Members", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/memberships/organization/{{orgId}}", "host": ["{{baseUrl}}"], "path": ["api", "memberships", "organization", "{{orgId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has members array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('members');", "    pm.expect(jsonData.members).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Remove Member", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/memberships/organization/{{orgId}}/member/{{memberId}}", "host": ["{{baseUrl}}"], "path": ["api", "memberships", "organization", "{{orgId}}", "member", "{{memberId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Member removed successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, {"name": "Task", "item": [{"name": "Create Task", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Task\",\n  \"description\": \"This is a test task\",\n  \"orgId\": \"{{orgId}}\",\n  \"dueAt\": \"2025-03-30T23:59:59Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/tasks", "host": ["{{baseUrl}}"], "path": ["api", "tasks"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "", "pm.test(\"Task created successfully\", function () {", "    pm.expect(jsonData).to.have.property('message', 'Task created successfully');", "    pm.expect(jsonData).to.have.property('task');", "    pm.expect(jsonData.task).to.have.property('id');", "    pm.environment.set(\"taskId\", jsonData.task.id);", "});"], "type": "text/javascript"}}]}, {"name": "Get Tasks by Organization", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/tasks/org/{{orgId}}", "host": ["{{baseUrl}}"], "path": ["api", "tasks", "org", "{{orgId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has tasks array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('tasks');", "    pm.expect(jsonData.tasks).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Task by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/tasks/{{taskId}}", "host": ["{{baseUrl}}"], "path": ["api", "tasks", "{{taskId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has task data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('task');", "    pm.expect(jsonData.task).to.have.property('id', pm.environment.get(\"taskId\"));", "    pm.expect(jsonData.task).to.have.property('title');", "});"], "type": "text/javascript"}}]}, {"name": "Update Task Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in-progress\"\n}"}, "url": {"raw": "{{baseUrl}}/api/tasks/{{taskId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "tasks", "{{taskId}}", "status"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Task status updated successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message', 'Task status updated successfully');", "    pm.expect(jsonData).to.have.property('task');", "    pm.expect(jsonData.task.status).to.equal('in-progress');", "});"], "type": "text/javascript"}}]}, {"name": "Assign Task", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assignedTo\": \"{{userId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/tasks/{{taskId}}/assign", "host": ["{{baseUrl}}"], "path": ["api", "tasks", "{{taskId}}", "assign"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Task assigned successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message', 'Task assigned successfully');", "    pm.expect(jsonData).to.have.property('task');", "    pm.expect(jsonData.task.assignedTo).to.equal(pm.environment.get(\"userId\"));", "});"], "type": "text/javascript"}}]}, {"name": "Update Task", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Task Title\",\n  \"description\": \"This task has been updated\",\n  \"dueAt\": \"2025-04-01T23:59:59Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/tasks/{{taskId}}", "host": ["{{baseUrl}}"], "path": ["api", "tasks", "{{taskId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Task updated successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message', 'Task updated successfully');", "    pm.expect(jsonData).to.have.property('task');", "    pm.expect(jsonData.task.title).to.equal('Updated Task Title');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Task", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/tasks/{{taskId}}", "host": ["{{baseUrl}}"], "path": ["api", "tasks", "{{taskId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Task deleted successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message', 'Task deleted successfully');", "});"], "type": "text/javascript"}}]}]}]}