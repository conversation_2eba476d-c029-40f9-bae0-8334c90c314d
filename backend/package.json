{"name": "backend", "module": "src/server.ts", "type": "module", "scripts": {"dev": "bun --watch src/server.ts", "build": "bun build ./src/server.ts --outdir ./dist --target node", "compile": "bun build --compile --minify --sourcemap ./src/server.ts --outfile ./dist/myapp --target node", "start": "IS_PROD=production bun run ./dist/server.js"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/bun": "latest", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.13.5", "prisma": "^6.4.1", "tsx": "^4.19.3"}, "peerDependencies": {"typescript": "^5.0.0"}, "dependencies": {"@prisma/client": "^6.4.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^4.21.2", "jsonwebtoken": "^9.0.2"}}